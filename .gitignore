# Dependencies
node_modules/
/.pnp
.pnp.js
.yarn/install-state.gz

# Testing
/coverage
/.nyc_output
/test-results/
/playwright-report/
/playwright/.cache/

# Build and Distribution
/build
/dist
/out/
/storybook-static
/analyze
web-build/
# 忽略所有build目录
**/build/
**/build.gradle.kts

# Expo
.expo/
expo-env.d.ts

# React Native
.kotlin/
*.orig.*

# Android (只忽略根目录，不忽略modules中的自定义原生代码)
/android/
/ios/

# Next.js
/.next/
next-env.d.ts

# Metro
.metro-health-check*

# TypeScript
*.tsbuildinfo

# Environment Variables
.env
.env*.local

# Debug and Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
*.log

# IDE and Editors
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
*.swp
*.swo
*~
.history

# OS Generated Files
.DS_Store
Thumbs.db

# Security and Certificates
*.key
*.pem
*.p8
*.p12
*.pfx
*.jks
*.keystore
*.mobileprovision
keystore.properties

# Database
*.db
*.sqlite
*.sqlite3

# Temporary Files
*.tmp
*.temp
.cache

# Backup Files
*.backup
*.bak

# Misc
.vercel
.claude

# Lock files (根据团队约定决定是否忽略)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml
credentials.json
.docs
debug-screenshots