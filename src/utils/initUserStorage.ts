import { updateKnowledgeStorageName } from '@/stores/knowledgeStore';
import { updateRecordingStorageName } from '@/stores/recordingStore';
import { getUserId } from './userIdUtils';

/**
 * 初始化用户存储
 * 当用户登录或应用启动时调用此函数，以确保所有存储使用正确的用户ID
 */
export const initUserStorage = async (): Promise<void> => {
  try {
    // 获取当前用户ID
    const userId = await getUserId();

    // 确保用户ID被记录在日志中，方便调试
    console.log('🔄 初始化用户存储: 用户ID =', userId);

    // 更新用户隔离的存储名称（原子性操作）
    await updateKnowledgeStorageName();
    await updateRecordingStorageName();

    // 清理缓存或执行其他需要的初始化操作
    console.log('✅ 用户存储初始化成功');
  } catch (error) {
    console.error('❌ 初始化用户存储失败:', error);
    throw error; // 重新抛出错误，让调用者知道初始化失败
  }
};

/**
 * 带重试机制的用户存储初始化
 * 解决原子性问题：如果部分操作失败，会重试整个过程
 */
export const initUserStorageWithRetry = async (
  maxRetries: number = 3
): Promise<void> => {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 尝试初始化用户存储 (第${attempt}次)`);

      // 执行完整的初始化
      await initUserStorage();

      console.log('✅ 用户存储初始化成功');
      return; // 成功则退出
    } catch (error) {
      lastError = error as Error;
      console.warn(`❌ 第${attempt}次初始化失败:`, lastError.message);

      if (attempt < maxRetries) {
        const delay = Math.min(1000 * attempt, 3000); // 递增延迟，最大3秒
        console.log(`⏳ ${delay}ms后重试...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  // 所有重试都失败了
  console.error(`❌ 用户存储初始化失败，已重试${maxRetries}次`);
  throw lastError || new Error('用户存储初始化失败');
};
