import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * 从 AsyncStorage 获取用户ID
 * @returns Promise<number> 用户ID，如果获取失败则返回默认值1
 */
export const getUserId = async (): Promise<number> => {
  try {
    const userInfo = await AsyncStorage.getItem('userInfo');
    if (!userInfo) {
      console.warn('用户信息未找到，使用默认用户ID');
      return 1;
    }
    const parsedUserInfo = JSON.parse(userInfo);
    const userId = Number(parsedUserInfo?.attributes?.userInfo?.userId) || 1;
    return userId;
  } catch (error) {
    console.error('获取用户ID失败:', error);
    return 1;
  }
};

/**
 * 生成基于用户ID的存储名称
 * @param baseName 基础存储名称
 * @returns Promise<string> 带用户ID的存储名称
 */
export const generateStorageName = async (
  baseName: string
): Promise<string> => {
  const userId = await getUserId();
  return `${baseName}_user_${userId}`;
};

/**
 * 获取当前用户信息
 * @returns Promise<any> 用户信息对象，如果不存在则返回null
 */
export const getUserInfo = async (): Promise<any> => {
  try {
    const userInfo = await AsyncStorage.getItem('userInfo');
    if (!userInfo) {
      return null;
    }
    return JSON.parse(userInfo);
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
};

/**
 * 保存用户信息
 * @param userInfo 用户信息对象
 * @returns Promise<void>
 */
export const saveUserInfo = async (userInfo: any): Promise<void> => {
  try {
    await AsyncStorage.setItem('userInfo', JSON.stringify(userInfo));
  } catch (error) {
    console.error('保存用户信息失败:', error);
    throw error;
  }
};

/**
 * 清除用户信息
 * @returns Promise<void>
 */
export const clearUserInfo = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem('userInfo');
  } catch (error) {
    console.error('清除用户信息失败:', error);
    throw error;
  }
};
