import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Share,
  TouchableOpacity,
  Image,
  Clipboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography } from '../../../styles';
import { Recording } from '@/types';
import { DialogUtil } from '@/utils/dialogUtil';

interface ChapterItem {
  title: string;
  time: string;
  content: string;
}

interface AISummaryContentProps {
  recording?: Recording;
  onTimePress?: (
    time: string,
    nextChapterTime?: string | null,
    segment?: number
  ) => void;
}

const AISummaryContent: React.FC<AISummaryContentProps> = ({
  recording,
  onTimePress,
}) => {
  // Initialize all sections as collapsed by default
  const [collapsedSections, setCollapsedSections] = useState<{
    [key: string]: boolean;
  }>({});

  // When recording or chapterOverview changes, initialize all sections as collapsed
  useEffect(() => {
    if (recording?.chapterOverview) {
      try {
        const data = JSON.parse(recording.chapterOverview);
        const initialState: { [key: string]: boolean } = {};

        // Set all sections to collapsed (true)
        data.outline.forEach((_: ChapterItem, index: number) => {
          initialState[`chapter-${index}`] = true;
        });

        setCollapsedSections(initialState);
      } catch (error) {
        console.error('Error initializing collapsed state:', error);
      }
    }
  }, [recording?.chapterOverview]);

  // Toggle collapse state for a specific section
  const toggleCollapse = (sectionId: string) => {
    setCollapsedSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId],
    }));
  };

  // Parse chapter overview from JSON
  const parseChapterOverview = () => {
    if (!recording?.chapterOverview) return { outline: [] };

    try {
      return JSON.parse(recording.chapterOverview);
    } catch (error) {
      console.error('Error parsing chapter overview:', error);
      return { outline: [] };
    }
  };

  // Parse time to find the closest segment
  const findClosestSegmentByTime = (time: string): number => {
    if (
      !recording?.transcriptionData ||
      recording.transcriptionData.length === 0
    ) {
      return -1;
    }

    // Convert the target time to seconds
    const targetTimeSeconds = parseTimeString(time);

    // Find the segment with the closest timestamp
    let closestSegmentIndex = 0;
    let minTimeDifference = Math.abs(
      parseTimeString(recording.transcriptionData[0].beginTime) -
        targetTimeSeconds
    );

    recording.transcriptionData.forEach((segment, index) => {
      const segmentTimeSeconds = parseTimeString(segment.beginTime);
      const timeDifference = Math.abs(segmentTimeSeconds - targetTimeSeconds);

      if (timeDifference < minTimeDifference) {
        minTimeDifference = timeDifference;
        closestSegmentIndex = index;
      }
    });

    return closestSegmentIndex;
  };

  // Parse time string to seconds
  const parseTimeString = (timeStr: string): number => {
    // Support formats: HH:MM:SS or MM:SS or SS
    const parts = timeStr.split(':').map(Number);
    if (parts.length === 3) {
      // HH:MM:SS
      return parts[0] * 3600 + parts[1] * 60 + parts[2];
    } else if (parts.length === 2) {
      // MM:SS
      return parts[0] * 60 + parts[1];
    } else if (parts.length === 1) {
      // SS
      return parts[0];
    }
    return 0;
  };

  // Handle time click to seek to that position
  const handleTimePress = (time: string, nextChapterTime?: string | null) => {
    if (onTimePress) {
      // Find the closest segment to this time
      const segmentIndex = findClosestSegmentByTime(time);

      // Pass the segment index along with the time
      onTimePress(time, nextChapterTime, segmentIndex);
    }
  };

  // Render interactive chapter overview
  const renderChapterOverview = () => {
    const chapterData = parseChapterOverview();

    if (!chapterData.outline || chapterData.outline.length === 0) {
      return (
        <View style={styles.markdownWrapper}>
          <Text style={styles.keyPointText}>暂无章节速览</Text>
        </View>
      );
    }

    return (
      <View style={styles.chaptersContainer}>
        {chapterData.outline.map((chapter: ChapterItem, index: number) => {
          const sectionId = `chapter-${index}`;
          const isCollapsed = collapsedSections[sectionId];
          const nextChapter = chapterData.outline[index + 1];

          return (
            <View key={index} style={styles.chapterItem}>
              <TouchableOpacity
                style={styles.chapterHeader}
                onPress={() => toggleCollapse(sectionId)}
              >
                <View style={styles.chapterTitleRow}>
                  <TouchableOpacity
                    style={styles.timeButton}
                    onPress={() =>
                      handleTimePress(chapter.time, nextChapter?.time)
                    }
                  >
                    <Text style={styles.chapterTime}>{chapter.time}</Text>
                  </TouchableOpacity>
                  <Text style={styles.chapterTitle}>{chapter.title}</Text>
                </View>
                <Ionicons
                  name={isCollapsed ? 'chevron-down' : 'chevron-up'}
                  size={20}
                  color={colors.text.secondary}
                />
              </TouchableOpacity>

              {!isCollapsed && (
                <View style={styles.chapterContent}>
                  <Text style={styles.chapterText}>{chapter.content}</Text>
                </View>
              )}
            </View>
          );
        })}
      </View>
    );
  };
  const handleShare = async () => {
    if (!recording?.fullSummary) return;

    try {
      await Share.share({
        url: 'https://www.hjlingxi.com/LCDP/#/userLogin',
        title: recording?.fullSummary,
        message: `分享Ai速览`,
      });
    } catch (error) {
      console.error('分享失败:', error);
    }
  };

  const handleCopy = async () => {
    if (!recording?.fullSummary) return;

    try {
      const content = `全文摘要
    ${recording?.fullSummary || ''}
章节速览
    ${recording?.chapterOverview || ''}`;
      await Clipboard.setString(content);
      DialogUtil.success('已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  const renderStatusContent = () => {
    if (
      recording?.transcriptionStatus === 'pending' ||
      !recording?.transcriptionStatus
    ) {
      return (
        <View style={styles.statusContainer}>
          <Image
            source={require('../../../../assets/images/icons/empty.png')}
            style={styles.statusImage}
            resizeMode="contain"
          />
          <Text style={styles.statusDescription}>
            暂无内容，请先完成录音转文字
          </Text>
        </View>
      );
    }

    if (recording?.transcriptionStatus === 'failed') {
      return (
        <View style={styles.statusContainer}>
          <Image
            source={require('../../../../assets/images/icons/error.png')}
            style={styles.statusImage}
            resizeMode="contain"
          />
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.statusDescription}>
              小兹提醒，前方有一点点堵车，
            </Text>
            <TouchableOpacity
              onPress={() => DialogUtil.alert('提示', '请稍后再试')}
            >
              <Text style={{ color: colors.primary }}>请点击重试</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // success state - show the actual content
    return (
      <>
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>结果由人工智能合成</Text>
            <View style={styles.actionButtons}>
              <TouchableOpacity
                onPress={handleCopy}
                style={styles.actionButton}
              >
                <Ionicons name="copy-outline" size={22} color="#414352" />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleShare}
                style={styles.actionButton}
              >
                <Ionicons name="share-outline" size={22} color="#414352" />
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.keyPointWrapper}>
            <Ionicons
              name="document-text-outline"
              size={24}
              color={colors.text.primary}
            />
            <Text style={styles.keyPointTitle}>全文摘要</Text>
          </View>
          <View style={styles.keyPointsContainer}>
            <Text style={styles.keyPointText}>
              {recording.fullSummary || '暂无摘要'}
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.keyPointWrapper}>
            <Ionicons
              name="document-text-outline"
              size={24}
              color={colors.text.primary}
            />
            <Text style={styles.keyPointTitle}>章节速览</Text>
          </View>
          {renderChapterOverview()}
        </View>
      </>
    );
  };

  return <View style={styles.contentWrapper}>{renderStatusContent()}</View>;
};

const styles = StyleSheet.create({
  contentWrapper: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 0, // 🎯 移除padding，让图标边缘精确对齐
  },
  sectionTitle: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 12,
  },
  keyPointsContainer: {
    // backgroundColor: colors.background.bg,
    // borderRadius: 12,
    // padding: 16,
  },
  keyPointWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  keyPointTitle: {
    fontSize: 20,
    marginLeft: 8,
    fontWeight: typography.fontWeight.semibold,
  },
  keyPointItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
    marginTop: 6,
    marginRight: 12,
  },
  keyPointText: {
    fontSize: 14,
    color: colors.text.primary,
    flex: 1,
    lineHeight: 20,
  },
  markdownWrapper: {
    // 移除调试样式
  },
  topicsContainer: {
    // backgroundColor: colors.background.bg,
    // borderRadius: 12,
    // padding: 16,
  },
  topicItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  topicName: {
    fontSize: 14,
    color: colors.text.primary,
    width: 80,
  },
  topicBar: {
    flex: 1,
    height: 8,
    backgroundColor: colors.border,
    borderRadius: 4,
    marginHorizontal: 12,
  },
  topicFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  topicCount: {
    fontSize: 14,
    color: colors.text.secondary,
    width: 20,
    textAlign: 'right',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
    backgroundColor: colors.background.bg,
    borderRadius: 12,
    padding: 16,
    flex: 1,
    marginHorizontal: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.text.secondary,
    marginTop: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: 2,
  },
  statusContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  statusImage: {
    width: 180,
    height: 180,
    marginBottom: 4,
  },
  statusDescription: {
    fontSize: 14,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  // Chapter overview styles
  chaptersContainer: {
    marginTop: 8,
  },
  chapterItem: {
    marginBottom: 12,
    backgroundColor: colors.background.bg,
    borderRadius: 12,
    overflow: 'hidden',
  },
  chapterHeader: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  chapterTitleRow: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginRight: 12,
  },
  chapterTime: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: typography.fontWeight.medium,
  },
  chapterTitle: {
    fontSize: 16,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    flex: 1,
  },
  chapterContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  chapterText: {
    fontSize: 14,
    color: colors.text.secondary,
    lineHeight: 20,
  },
});

export default AISummaryContent;
