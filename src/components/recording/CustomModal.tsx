import React, { useRef, useEffect } from 'react';
import {
  StyleSheet,
  Animated,
  Dimensions,
  SafeAreaView,
  StatusBar,
  Platform,
  TouchableOpacity,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useMediaPlayerContext } from '../../contexts/MediaPlayerContext';

interface CustomModalProps {
  visible: boolean;
  children: React.ReactNode;
  onClose: () => void;
}

const { width, height } = Dimensions.get('window');

// 混合方案：SafeAreaView + 手动计算保底
const getStatusBarHeight = () => {
  if (Platform.OS === 'ios') {
    // iOS设备的状态栏高度
    return 44; // 标准状态栏高度，刘海屏会由SafeAreaView自动处理
  } else {
    // Android设备的状态栏高度
    return StatusBar.currentHeight || 24; // 获取实际高度，fallback到24px
  }
};

const CustomModal: React.FC<CustomModalProps> = ({
  visible,
  children,
  onClose,
}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const insets = useSafeAreaInsets(); // 获取安全区域信息
  const { playerHeight } = useMediaPlayerContext(); // 获取播放器实际高度
  const translateAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  // 🎯 动态计算播放器占用空间：播放器实际高度 + 底部安全区域
  const playerTotalHeight = playerHeight + insets.bottom;

  useEffect(() => {
    if (visible) {
      // 打开动画：从右上角从小到大，同时淡入
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(translateAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // 关闭动画：从小到大，同时淡出
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(translateAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  // 计算动画变换值
  const scale = scaleAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 1],
  });

  const translateX = translateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [width * 0.5, 0],
  });

  const translateY = translateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-height * 0.5, 0],
  });

  if (!visible) {
    return null;
  }

  return (
    <SafeAreaView style={[styles.container, { bottom: playerTotalHeight }]}>
      {/* Background overlay to handle taps outside the modal */}
      <TouchableOpacity
        style={styles.backdrop}
        activeOpacity={1}
        onPress={onClose}
      />

      {/* Modal内容 - SafeAreaView自动处理安全区域 */}
      <Animated.View
        style={[
          styles.modalContent,
          {
            opacity: opacityAnim,
            transform: [{ scale }, { translateX }, { translateY }],
          },
        ]}
      >
        {children}
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    // bottom值现在通过动态计算传入，适配不同设备的安全区域
    zIndex: 1000, // 确保在其他内容之上，但不会影响播放器
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },

  modalContent: {
    flex: 1, // 填满SafeAreaView
    marginHorizontal: 8, // 左右8px间距
    marginTop: getStatusBarHeight(), // 🎯 混合方案：手动计算作为保底，SafeAreaView失效时生效
    backgroundColor: '#FFFFFF', // 白色背景
    borderRadius: 24, // 24px圆角，匹配设计稿
    overflow: 'hidden',
    // 阴影效果，正确翻译CSS: box-shadow: 0px 8px 24px 0px rgba(100,103,122,0.24)
    shadowColor: '#64677A', // rgba(100,103,122) 转换为hex
    shadowOffset: {
      width: 0, // x偏移
      height: 8, // y偏移
    },
    shadowOpacity: 0.24, // 透明度
    shadowRadius: 24, // 模糊半径
    elevation: 12, // Android阴影
  },
});

export default CustomModal;
