import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { ExpoLocationService } from '@/services/location/ExpoLocationService';

export interface LocationTestResult {
  timestamp: number;
  type: string;
  data: any;
  success: boolean;
  error?: string;
}

export default function ExpoLocationTest() {
  const [results, setResults] = useState<LocationTestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (
    type: string,
    data: any,
    success = true,
    error?: string
  ) => {
    const result: LocationTestResult = {
      timestamp: Date.now(),
      type,
      data,
      success,
      error,
    };
    setResults((prev) => [result, ...prev]);
  };

  const testPermissions = async () => {
    setIsLoading(true);
    try {
      const status = await ExpoLocationService.getPermissions();
      addResult('权限检查', status);
    } catch (error) {
      addResult('权限检查', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const requestPermissions = async () => {
    setIsLoading(true);
    try {
      const result = await ExpoLocationService.requestPermissions();
      addResult('权限请求', result);
    } catch (error) {
      addResult('权限请求', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const testLocationEnabled = async () => {
    setIsLoading(true);
    try {
      const enabled = await ExpoLocationService.isLocationEnabled();
      addResult('位置服务检查', { enabled });
    } catch (error) {
      addResult('位置服务检查', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentPosition = async () => {
    setIsLoading(true);
    try {
      const position = await ExpoLocationService.getCurrentPosition({
        accuracy: 'high',
        timeout: 10000,
        maximumAge: 5000,
      });
      addResult('获取位置', position);
    } catch (error) {
      addResult('获取位置', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  const formatResult = (result: LocationTestResult) => {
    return JSON.stringify(result.data, null, 2);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Expo Location Module 测试</Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testPermissions}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>检查权限</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={requestPermissions}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>请求权限</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testLocationEnabled}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>检查位置服务</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={getCurrentPosition}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>获取位置</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>清空结果</Text>
        </TouchableOpacity>
      </View>

      {isLoading && <Text style={styles.loadingText}>正在执行测试...</Text>}

      <ScrollView style={styles.resultsContainer}>
        {results.map((result, index) => (
          <View
            key={index}
            style={[
              styles.resultItem,
              result.success ? styles.successResult : styles.errorResult,
            ]}
          >
            <Text style={styles.resultHeader}>
              {result.type} - {new Date(result.timestamp).toLocaleTimeString()}
            </Text>
            {result.success ? (
              <Text style={styles.resultData}>{formatResult(result)}</Text>
            ) : (
              <Text style={styles.errorText}>错误: {result.error}</Text>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    marginVertical: 4,
    minWidth: '48%',
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#007AFF',
    marginBottom: 10,
  },
  resultsContainer: {
    flex: 1,
  },
  resultItem: {
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  successResult: {
    backgroundColor: '#E8F5E8',
    borderColor: '#4CAF50',
  },
  errorResult: {
    backgroundColor: '#FFE8E8',
    borderColor: '#F44336',
  },
  resultHeader: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  resultData: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: '#666',
  },
  errorText: {
    fontSize: 12,
    color: '#F44336',
  },
});
