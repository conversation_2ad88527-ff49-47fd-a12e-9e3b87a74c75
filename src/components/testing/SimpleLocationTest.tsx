import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  NativeModules,
} from 'react-native';
import * as Location from 'expo-location';

export default function SimpleLocationTest() {
  const [results, setResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (
    type: string,
    data: any,
    success = true,
    error?: string
  ) => {
    const result = {
      timestamp: Date.now(),
      type,
      data,
      success,
      error,
    };
    setResults((prev) => [result, ...prev]);
  };

  const testExpoLocation = async () => {
    setIsLoading(true);
    try {
      // 1. 检查权限
      const { status } = await Location.requestForegroundPermissionsAsync();
      addResult('Expo权限请求', { status });

      if (status === 'granted') {
        // 2. 获取位置
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
          maximumAge: 5000,
        });
        addResult('Expo获取位置', location);
      }
    } catch (error) {
      addResult('Expo测试', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const testNativeLocation = async () => {
    setIsLoading(true);
    try {
      // 测试我们的原生模块

      // 检查模块状态
      const moduleStatus = {
        keys: Object.keys(NativeModules).filter(
          (key) => key.includes('Location') || key.includes('location')
        ),
        hasNativeLocation: !!NativeModules.NativeLocationModule,
        hasExpoLocation: !!NativeModules.ExpoLocation,
        allModules: Object.keys(NativeModules).length,
      };

      addResult('NativeModules检查', moduleStatus);

      // 测试现有的NativeLocationModule
      if (NativeModules.NativeLocationModule) {
        try {
          const hasPermissions =
            await NativeModules.NativeLocationModule.hasLocationPermissions();
          const hasServices =
            await NativeModules.NativeLocationModule.hasServicesEnabled();

          addResult('NativeLocationModule状态', {
            hasPermissions,
            hasServices,
          });

          if (hasPermissions && hasServices) {
            // 根据错误信息，accuracy范围是[0,2]
            const locationResult =
              await NativeModules.NativeLocationModule.getCurrentLocation({
                accuracy: 2, // 高精度 (范围0-2，2为最高)
                timeout: 10000,
                maximumAge: 5000,
              });
            addResult('NativeLocationModule定位', locationResult);
          }
        } catch (error) {
          addResult('NativeLocationModule测试', null, false, String(error));
        }
      }
    } catch (error) {
      addResult('原生模块测试', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>位置模块测试</Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testExpoLocation}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>测试Expo定位</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testNativeLocation}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>检查原生模块</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>清空结果</Text>
        </TouchableOpacity>
      </View>

      {isLoading && <Text style={styles.loadingText}>正在执行测试...</Text>}

      <ScrollView style={styles.resultsContainer}>
        {results.map((result, index) => (
          <View
            key={index}
            style={[
              styles.resultItem,
              result.success ? styles.successResult : styles.errorResult,
            ]}
          >
            <Text style={styles.resultHeader}>
              {result.type} - {new Date(result.timestamp).toLocaleTimeString()}
            </Text>
            {result.success ? (
              <Text style={styles.resultData}>
                {JSON.stringify(result.data, null, 2)}
              </Text>
            ) : (
              <Text style={styles.errorText}>错误: {result.error}</Text>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    marginVertical: 4,
    minWidth: '48%',
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#007AFF',
    marginBottom: 10,
  },
  resultsContainer: {
    flex: 1,
  },
  resultItem: {
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  successResult: {
    backgroundColor: '#E8F5E8',
    borderColor: '#4CAF50',
  },
  errorResult: {
    backgroundColor: '#FFE8E8',
    borderColor: '#F44336',
  },
  resultHeader: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  resultData: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: '#666',
  },
  errorText: {
    fontSize: 12,
    color: '#F44336',
  },
});
