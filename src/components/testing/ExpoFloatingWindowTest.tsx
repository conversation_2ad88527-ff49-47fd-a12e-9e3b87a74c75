import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  TextInput,
} from 'react-native';

// 导入新的Expo浮窗模块
import ExpoFloatingWindow, {
  FloatingWindowConfig,
  FloatingWindowResult,
  PermissionStatus,
  Subscription,
  FloatingWindowInfo,
  WindowMoveEvent,
  WindowClickEvent,
} from 'expo-floating-window';

export interface FloatingWindowTestResult {
  timestamp: number;
  type: string;
  data: any;
  success: boolean;
  error?: string;
}

export default function ExpoFloatingWindowTest() {
  const [results, setResults] = useState<FloatingWindowTestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [permissionStatus, setPermissionStatus] =
    useState<PermissionStatus | null>(null);
  const [createdWindows, setCreatedWindows] = useState<FloatingWindowInfo[]>(
    []
  );

  // 表单数据
  const [windowConfig, setWindowConfig] = useState({
    width: 300,
    height: 200,
    x: 100,
    y: 100,
    title: '测试浮窗',
    content: '这是一个测试浮窗',
  });

  // 引用管理
  const moveSubscription = useRef<Subscription | null>(null);
  const clickSubscription = useRef<Subscription | null>(null);

  const addResult = (
    type: string,
    data: any,
    success = true,
    error?: string
  ) => {
    const result: FloatingWindowTestResult = {
      timestamp: Date.now(),
      type,
      data,
      success,
      error,
    };
    setResults((prev) => [result, ...prev]);
  };

  // 清理订阅
  const clearSubscriptions = () => {
    if (moveSubscription.current) {
      moveSubscription.current.remove();
      moveSubscription.current = null;
    }
    if (clickSubscription.current) {
      clickSubscription.current.remove();
      clickSubscription.current = null;
    }
  };

  // 检查权限
  const checkPermissions = async () => {
    setIsLoading(true);
    try {
      const status = await ExpoFloatingWindow.checkPermissions();
      setPermissionStatus(status);
      addResult('权限检查', status);
    } catch (error) {
      addResult('权限检查', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  // 请求权限
  const requestPermissions = async () => {
    setIsLoading(true);
    try {
      const status = await ExpoFloatingWindow.requestPermissions();
      setPermissionStatus(status);
      addResult('权限请求', status);

      if (!status.granted) {
        Alert.alert(
          '权限需要手动授予',
          '请在系统设置中为应用开启悬浮窗权限，然后返回应用重新检查权限。',
          [{ text: '确定' }]
        );
      }
    } catch (error) {
      addResult('权限请求', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  // 创建浮窗
  const createWindow = async () => {
    setIsLoading(true);
    try {
      if (!permissionStatus?.granted) {
        Alert.alert('提示', '请先获取悬浮窗权限');
        setIsLoading(false);
        return;
      }

      const config: FloatingWindowConfig = {
        width: windowConfig.width,
        height: windowConfig.height,
        x: windowConfig.x,
        y: windowConfig.y,
        title: windowConfig.title,
        content: windowConfig.content,
        draggable: true,
      };

      const result: FloatingWindowResult =
        await ExpoFloatingWindow.createWindow(config);
      addResult('创建浮窗', { config, result });

      if (result.success && result.windowId) {
        // 更新窗口列表
        refreshWindowList();
      }
    } catch (error) {
      addResult('创建浮窗', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  // 刷新窗口列表
  const refreshWindowList = async () => {
    try {
      const windows = await ExpoFloatingWindow.getAllWindows();
      setCreatedWindows(windows);
      addResult('获取窗口列表', { count: windows.length, windows });
    } catch (error) {
      addResult('获取窗口列表', null, false, String(error));
    }
  };

  // 显示浮窗
  const showWindow = async (windowId: string) => {
    setIsLoading(true);
    try {
      const result = await ExpoFloatingWindow.showWindow(windowId);
      addResult('显示浮窗', { windowId, result });
      if (result.success) {
        refreshWindowList();
      }
    } catch (error) {
      addResult('显示浮窗', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  // 隐藏浮窗
  const hideWindow = async (windowId: string) => {
    setIsLoading(true);
    try {
      const result = await ExpoFloatingWindow.hideWindow(windowId);
      addResult('隐藏浮窗', { windowId, result });
      if (result.success) {
        refreshWindowList();
      }
    } catch (error) {
      addResult('隐藏浮窗', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  // 关闭浮窗
  const closeWindow = async (windowId: string) => {
    setIsLoading(true);
    try {
      const result = await ExpoFloatingWindow.closeWindow(windowId);
      addResult('关闭浮窗', { windowId, result });
      if (result.success) {
        refreshWindowList();
      }
    } catch (error) {
      addResult('关闭浮窗', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  // 设置事件监听
  const setupEventListeners = () => {
    // 清理旧的监听器
    clearSubscriptions();

    // 监听窗口移动
    moveSubscription.current = ExpoFloatingWindow.addMoveListener(
      (event: WindowMoveEvent) => {
        addResult('窗口移动事件', event);
      }
    );

    // 监听窗口点击
    clickSubscription.current = ExpoFloatingWindow.addClickListener(
      (event: WindowClickEvent) => {
        addResult('窗口点击事件', event);
      }
    );

    addResult('事件监听器设置', { move: true, click: true });
  };

  const clearResults = () => {
    setResults([]);
  };

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      clearSubscriptions();
      ExpoFloatingWindow.removeAllListeners();
    };
  }, []);

  const formatResult = (result: FloatingWindowTestResult) => {
    return JSON.stringify(result.data, null, 2);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Expo 浮窗模块测试</Text>

      {/* 权限状态 */}
      {permissionStatus && (
        <View style={styles.statusContainer}>
          <Text style={styles.statusTitle}>权限状态</Text>
          <Text style={styles.statusText}>
            状态: {permissionStatus.status} | 已授予:{' '}
            {permissionStatus.granted ? '是' : '否'} | 可请求:{' '}
            {permissionStatus.canRequest ? '是' : '否'}
          </Text>
        </View>
      )}

      {/* 浮窗配置 */}
      <View style={styles.configContainer}>
        <Text style={styles.configTitle}>浮窗配置</Text>
        <View style={styles.configRow}>
          <Text style={styles.configLabel}>宽度:</Text>
          <TextInput
            style={styles.configInput}
            value={String(windowConfig.width)}
            onChangeText={(text) =>
              setWindowConfig((prev) => ({
                ...prev,
                width: parseInt(text) || 300,
              }))
            }
            keyboardType="numeric"
          />
          <Text style={styles.configLabel}>高度:</Text>
          <TextInput
            style={styles.configInput}
            value={String(windowConfig.height)}
            onChangeText={(text) =>
              setWindowConfig((prev) => ({
                ...prev,
                height: parseInt(text) || 200,
              }))
            }
            keyboardType="numeric"
          />
        </View>
        <View style={styles.configRow}>
          <Text style={styles.configLabel}>X:</Text>
          <TextInput
            style={styles.configInput}
            value={String(windowConfig.x)}
            onChangeText={(text) =>
              setWindowConfig((prev) => ({ ...prev, x: parseInt(text) || 100 }))
            }
            keyboardType="numeric"
          />
          <Text style={styles.configLabel}>Y:</Text>
          <TextInput
            style={styles.configInput}
            value={String(windowConfig.y)}
            onChangeText={(text) =>
              setWindowConfig((prev) => ({ ...prev, y: parseInt(text) || 100 }))
            }
            keyboardType="numeric"
          />
        </View>
        <TextInput
          style={styles.configTextInput}
          placeholder="浮窗标题"
          value={windowConfig.title}
          onChangeText={(text) =>
            setWindowConfig((prev) => ({ ...prev, title: text }))
          }
        />
        <TextInput
          style={styles.configTextInput}
          placeholder="浮窗内容"
          value={windowConfig.content}
          onChangeText={(text) =>
            setWindowConfig((prev) => ({ ...prev, content: text }))
          }
        />
      </View>

      {/* 操作按钮 */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={checkPermissions}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>检查权限</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={requestPermissions}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>请求权限</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.button,
            styles.createButton,
            isLoading && styles.buttonDisabled,
          ]}
          onPress={createWindow}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>创建浮窗</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={setupEventListeners}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>设置监听器</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={refreshWindowList}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>刷新列表</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>清空结果</Text>
        </TouchableOpacity>
      </View>

      {/* 已创建的浮窗列表 */}
      {createdWindows.length > 0 && (
        <View style={styles.windowListContainer}>
          <Text style={styles.windowListTitle}>
            已创建的浮窗 ({createdWindows.length})
          </Text>
          {createdWindows.map((window, index) => (
            <View key={window.id} style={styles.windowItem}>
              <Text style={styles.windowTitle}>
                {window.title || `浮窗 ${index + 1}`}
              </Text>
              <Text style={styles.windowInfo}>
                {window.width}x{window.height} at ({window.x}, {window.y}) |
                {window.visible ? ' 可见' : ' 隐藏'}
              </Text>
              <View style={styles.windowActions}>
                <TouchableOpacity
                  style={[styles.smallButton, styles.showButton]}
                  onPress={() => showWindow(window.id)}
                >
                  <Text style={styles.smallButtonText}>显示</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.smallButton, styles.hideButton]}
                  onPress={() => hideWindow(window.id)}
                >
                  <Text style={styles.smallButtonText}>隐藏</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.smallButton, styles.closeButton]}
                  onPress={() => closeWindow(window.id)}
                >
                  <Text style={styles.smallButtonText}>关闭</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>
      )}

      {isLoading && <Text style={styles.loadingText}>正在执行操作...</Text>}

      <ScrollView style={styles.resultsContainer}>
        {results.map((result, index) => (
          <View
            key={index}
            style={[
              styles.resultItem,
              result.success ? styles.successResult : styles.errorResult,
            ]}
          >
            <Text style={styles.resultHeader}>
              {result.type} - {new Date(result.timestamp).toLocaleTimeString()}
            </Text>
            {result.success ? (
              <Text style={styles.resultData}>{formatResult(result)}</Text>
            ) : (
              <Text style={styles.errorText}>错误: {result.error}</Text>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  statusContainer: {
    backgroundColor: '#e8f4f8',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  statusTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  statusText: {
    fontSize: 12,
    color: '#666',
  },
  configContainer: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  configTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  configRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  configLabel: {
    fontSize: 12,
    color: '#666',
    minWidth: 40,
  },
  configInput: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 4,
    fontSize: 12,
    width: 60,
    marginHorizontal: 8,
    textAlign: 'center',
  },
  configTextInput: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    fontSize: 12,
    marginBottom: 8,
  },
  windowListContainer: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  windowListTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  windowItem: {
    backgroundColor: '#f8f8f8',
    padding: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  windowTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
  },
  windowInfo: {
    fontSize: 10,
    color: '#666',
    marginTop: 2,
  },
  windowActions: {
    flexDirection: 'row',
    marginTop: 8,
  },
  smallButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  showButton: {
    backgroundColor: '#4CAF50',
  },
  hideButton: {
    backgroundColor: '#FF9800',
  },
  closeButton: {
    backgroundColor: '#F44336',
  },
  smallButtonText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    marginVertical: 4,
    minWidth: '30%',
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  createButton: {
    backgroundColor: '#4CAF50',
  },
  clearButton: {
    backgroundColor: '#FF9800',
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#007AFF',
    marginBottom: 10,
  },
  resultsContainer: {
    flex: 1,
  },
  resultItem: {
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  successResult: {
    backgroundColor: '#E8F5E8',
    borderColor: '#4CAF50',
  },
  errorResult: {
    backgroundColor: '#FFE8E8',
    borderColor: '#F44336',
  },
  resultHeader: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  resultData: {
    fontSize: 10,
    fontFamily: 'monospace',
    color: '#666',
  },
  errorText: {
    fontSize: 10,
    color: '#F44336',
  },
});
