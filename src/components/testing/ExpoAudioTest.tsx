import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  PermissionsAndroid,
} from 'react-native';

// 导入新的Expo录音模块
import ExpoRealtimeAudio, {
  AudioConfig,
  AudioResult,
  AudioStreamData,
  Subscription,
} from 'expo-realtime-audio';

export interface AudioTestResult {
  timestamp: number;
  type: string;
  data: any;
  success: boolean;
  error?: string;
}

export default function ExpoAudioTest() {
  const [results, setResults] = useState<AudioTestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [audioStats, setAudioStats] = useState({
    totalPackets: 0,
    totalBytes: 0,
    avgPacketSize: 0,
    recordingDuration: 0,
  });

  // 引用管理
  const audioDataSubscription = useRef<Subscription | null>(null);
  const errorSubscription = useRef<Subscription | null>(null);
  const startTimeRef = useRef<number>(0);
  const statsInterval = useRef<NodeJS.Timeout | null>(null);

  const addResult = (
    type: string,
    data: any,
    success = true,
    error?: string
  ) => {
    const result: AudioTestResult = {
      timestamp: Date.now(),
      type,
      data,
      success,
      error,
    };
    setResults((prev) => [result, ...prev]);
  };

  // 清理订阅
  const clearSubscriptions = () => {
    if (audioDataSubscription.current) {
      audioDataSubscription.current.remove();
      audioDataSubscription.current = null;
    }
    if (errorSubscription.current) {
      errorSubscription.current.remove();
      errorSubscription.current = null;
    }
    if (statsInterval.current) {
      clearInterval(statsInterval.current);
      statsInterval.current = null;
    }
  };

  // 测试Expo录音模块
  const testExpoAudio = async () => {
    setIsLoading(true);
    try {
      // 检查Expo录音模块状态
      const isRecording = await ExpoRealtimeAudio.isRecording();
      const duration = await ExpoRealtimeAudio.getRecordingDuration();

      const moduleStatus = {
        isRecording,
        duration,
        moduleAvailable: true,
      };

      addResult('Expo录音模块检查', moduleStatus);
    } catch (error) {
      addResult('Expo录音模块测试', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  // 开始录音测试（使用Expo模块）
  const startRecordingTest = async () => {
    setIsLoading(true);
    try {
      // 清理之前的订阅
      clearSubscriptions();

      // 设置音频数据监听
      let packetCount = 0;
      let totalBytes = 0;
      startTimeRef.current = Date.now();

      audioDataSubscription.current = ExpoRealtimeAudio.addAudioDataListener(
        (data: AudioStreamData) => {
          packetCount++;
          totalBytes += data.size;

          if (packetCount % 25 === 0) {
            // 每25个包(约1秒)更新一次UI
            setAudioStats((prev) => ({
              ...prev,
              totalPackets: packetCount,
              totalBytes,
              avgPacketSize: Math.round(totalBytes / packetCount),
              recordingDuration: Math.floor(
                (Date.now() - startTimeRef.current) / 1000
              ),
            }));
          }
        }
      );

      // 设置错误监听
      errorSubscription.current = ExpoRealtimeAudio.addErrorListener(
        (error) => {
          addResult('录音错误', { error: error.message }, false);
        }
      );

      // 开始录音
      const config: AudioConfig = {
        sampleRate: 16000,
        interval: 40,
        encoding: 'PCM_16BIT',
        enableForegroundService: true,
      };

      const result: AudioResult =
        await ExpoRealtimeAudio.startRecording(config);
      addResult('开始录音', { config, result });

      if (result.success) {
        setIsRecording(true);

        // 开始统计更新
        statsInterval.current = setInterval(async () => {
          const duration = await ExpoRealtimeAudio.getRecordingDuration();
          setAudioStats((prev) => ({
            ...prev,
            recordingDuration: duration,
          }));
        }, 1000);
      }
    } catch (error) {
      addResult('开始录音', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  // 停止录音测试
  const stopRecordingTest = async () => {
    setIsLoading(true);
    try {
      const result: AudioResult = await ExpoRealtimeAudio.stopRecording();
      addResult('停止录音', result);

      // 清理状态
      setIsRecording(false);
      clearSubscriptions();

      // 显示最终统计
      const finalDuration = await ExpoRealtimeAudio.getRecordingDuration();
      const finalStats = { ...audioStats, recordingDuration: finalDuration };
      addResult('录音统计', finalStats);
    } catch (error) {
      addResult('停止录音', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  // 权限测试
  const testPermissions = async () => {
    setIsLoading(true);
    try {
      // 这里应该测试录音权限，但我们先检查应用权限状态

      const permission = PermissionsAndroid.PERMISSIONS.RECORD_AUDIO;
      const hasPermission = await PermissionsAndroid.check(permission);

      addResult('录音权限检查', {
        permission,
        hasPermission,
        status: hasPermission ? 'granted' : 'denied',
      });

      if (!hasPermission) {
        const requestResult = await PermissionsAndroid.request(permission);
        addResult('权限请求', { requestResult });
      }
    } catch (error) {
      addResult('权限测试', null, false, String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
    setAudioStats({
      totalPackets: 0,
      totalBytes: 0,
      avgPacketSize: 0,
      recordingDuration: 0,
    });
  };

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      clearSubscriptions();
    };
  }, []);

  const formatBytes = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const formatResult = (result: AudioTestResult) => {
    return JSON.stringify(result.data, null, 2);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Expo 录音模块测试</Text>

      {/* 实时统计 */}
      {isRecording && (
        <View style={styles.statsContainer}>
          <Text style={styles.statsTitle}>录音统计 (实时)</Text>
          <Text style={styles.statsText}>
            时长: {audioStats.recordingDuration}秒
          </Text>
          <Text style={styles.statsText}>包数: {audioStats.totalPackets}</Text>
          <Text style={styles.statsText}>
            总大小: {formatBytes(audioStats.totalBytes)}
          </Text>
          <Text style={styles.statsText}>
            平均包大小: {audioStats.avgPacketSize} 字节
          </Text>
        </View>
      )}

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testPermissions}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>检查权限</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={testExpoAudio}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>检查Expo音频模块</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.button,
            isRecording ? styles.recordingButton : styles.startButton,
            isLoading && styles.buttonDisabled,
          ]}
          onPress={isRecording ? stopRecordingTest : startRecordingTest}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isRecording ? '停止录音' : '开始录音'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>清空结果</Text>
        </TouchableOpacity>
      </View>

      {isLoading && <Text style={styles.loadingText}>正在执行测试...</Text>}

      <ScrollView style={styles.resultsContainer}>
        {results.map((result, index) => (
          <View
            key={index}
            style={[
              styles.resultItem,
              result.success ? styles.successResult : styles.errorResult,
            ]}
          >
            <Text style={styles.resultHeader}>
              {result.type} - {new Date(result.timestamp).toLocaleTimeString()}
            </Text>
            {result.success ? (
              <Text style={styles.resultData}>{formatResult(result)}</Text>
            ) : (
              <Text style={styles.errorText}>错误: {result.error}</Text>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  statsContainer: {
    backgroundColor: '#e8f4f8',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  statsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  statsText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    marginVertical: 4,
    minWidth: '48%',
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  startButton: {
    backgroundColor: '#4CAF50',
  },
  recordingButton: {
    backgroundColor: '#F44336',
  },
  clearButton: {
    backgroundColor: '#FF9800',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#007AFF',
    marginBottom: 10,
  },
  resultsContainer: {
    flex: 1,
  },
  resultItem: {
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  successResult: {
    backgroundColor: '#E8F5E8',
    borderColor: '#4CAF50',
  },
  errorResult: {
    backgroundColor: '#FFE8E8',
    borderColor: '#F44336',
  },
  resultHeader: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  resultData: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: '#666',
  },
  errorText: {
    fontSize: 12,
    color: '#F44336',
  },
});
