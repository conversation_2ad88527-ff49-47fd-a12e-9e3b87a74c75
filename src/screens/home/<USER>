import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
  StatusBar,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { colors, typography, spacing } from '../../styles';
import { TabParamList } from '../../types/navigation';
import SimpleLocationTest from '../../components/testing/SimpleLocationTest';
import ExpoAudioTest from '../../components/testing/ExpoAudioTest';
import ExpoFloatingWindowTest from '../../components/testing/ExpoFloatingWindowTest';

const { width } = Dimensions.get('window');
const GRID_ITEM_WIDTH = (width - spacing.lg * 3) / 2;

interface QuickActionItem {
  id: string;
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  onPress: () => void;
}

type HomeScreenNavigationProp = BottomTabNavigationProp<TabParamList>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const [refreshing, setRefreshing] = useState(false);
  const [showLocationTest, setShowLocationTest] = useState(false);
  const [showAudioTest, setShowAudioTest] = useState(false);
  const [showFloatingWindowTest, setShowFloatingWindowTest] = useState(false);

  const quickActions: QuickActionItem[] = [
    {
      id: 'recording',
      title: '开始录音',
      icon: 'mic',
      color: colors.error,
      onPress: () => {
        navigation.navigate('RecordingTab');
      },
    },
    {
      id: 'writing',
      title: 'AI写作',
      icon: 'create',
      color: colors.primary,
      onPress: () => {
        navigation.navigate('WritingTab');
      },
    },
    {
      id: 'knowledge',
      title: '知识库',
      icon: 'library',
      color: colors.accent,
      onPress: () => {
        navigation.navigate('KnowledgeTab');
      },
    },
    {
      id: 'import',
      title: '导入文件',
      icon: 'cloud-upload',
      color: colors.success,
      onPress: () => {
        navigation.navigate('KnowledgeTab');
      },
    },
    {
      id: 'test-location',
      title: '位置测试',
      icon: 'location',
      color: '#FF9500',
      onPress: () => {
        setShowLocationTest(true);
      },
    },
    {
      id: 'test-audio',
      title: '录音测试',
      icon: 'volume-high',
      color: '#9C27B0',
      onPress: () => {
        setShowAudioTest(true);
      },
    },
    {
      id: 'test-floating-window',
      title: '浮窗测试',
      icon: 'layers',
      color: '#795548',
      onPress: () => {
        setShowFloatingWindowTest(true);
      },
    },
  ];

  const recentItems = [
    {
      id: '1',
      title: '会议录音_2025-07-15',
      type: 'recording',
      time: '2小时前',
    },
    {
      id: '2',
      title: '产品需求文档',
      type: 'document',
      time: '昨天',
    },
    {
      id: '3',
      title: '营销文案草稿',
      type: 'writing',
      time: '3天前',
    },
  ];

  const renderQuickAction = (item: QuickActionItem) => (
    <TouchableOpacity
      key={item.id}
      style={[styles.quickActionItem, { backgroundColor: item.color + '15' }]}
      onPress={item.onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.iconContainer, { backgroundColor: item.color }]}>
        <Ionicons name={item.icon} size={24} color="white" />
      </View>
      <Text style={styles.quickActionTitle}>{item.title}</Text>
    </TouchableOpacity>
  );

  const getItemIcon = (type: string) => {
    switch (type) {
      case 'recording':
        return 'mic';
      case 'document':
        return 'document-text';
      case 'writing':
        return 'create';
      default:
        return 'document';
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // TODO: 刷新数据
      await new Promise((resolve) => setTimeout(resolve, 1500));
    } finally {
      setRefreshing(false);
    }
  }, []);

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={colors.background.primary}
      />
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* 欢迎区域 */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>欢迎回来！</Text>
          <Text style={styles.welcomeSubtitle}>
            今天是{' '}
            {new Date().toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              weekday: 'long',
            })}
          </Text>
        </View>

        {/* 快捷操作 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>快捷操作</Text>
          <View style={styles.quickActionsGrid}>
            {quickActions.map(renderQuickAction)}
          </View>
        </View>

        {/* 最近使用 */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>最近使用</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>查看全部</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.recentList}>
            {recentItems.map((item) => (
              <TouchableOpacity key={item.id} style={styles.recentItem}>
                <View style={styles.recentItemLeft}>
                  <View style={styles.recentItemIcon}>
                    <Ionicons
                      name={getItemIcon(item.type)}
                      size={20}
                      color={colors.primary}
                    />
                  </View>
                  <View style={styles.recentItemInfo}>
                    <Text style={styles.recentItemTitle}>{item.title}</Text>
                    <Text style={styles.recentItemTime}>{item.time}</Text>
                  </View>
                </View>
                <Ionicons
                  name="chevron-forward"
                  size={16}
                  color={colors.text.secondary}
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 统计信息 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>使用统计</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>12</Text>
              <Text style={styles.statLabel}>录音文件</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>8</Text>
              <Text style={styles.statLabel}>知识文档</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>5</Text>
              <Text style={styles.statLabel}>AI文章</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* 位置测试模态框 */}
      <Modal
        visible={showLocationTest}
        animationType="slide"
        presentationStyle="formSheet"
        onRequestClose={() => setShowLocationTest(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>位置模块测试</Text>
            <TouchableOpacity
              onPress={() => setShowLocationTest(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color={colors.text.primary} />
            </TouchableOpacity>
          </View>
          <SimpleLocationTest />
        </View>
      </Modal>

      {/* 录音测试模态框 */}
      <Modal
        visible={showAudioTest}
        animationType="slide"
        presentationStyle="formSheet"
        onRequestClose={() => setShowAudioTest(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>录音模块测试</Text>
            <TouchableOpacity
              onPress={() => setShowAudioTest(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color={colors.text.primary} />
            </TouchableOpacity>
          </View>
          <ExpoAudioTest />
        </View>
      </Modal>

      {/* 浮窗测试模态框 */}
      <Modal
        visible={showFloatingWindowTest}
        animationType="slide"
        presentationStyle="formSheet"
        onRequestClose={() => setShowFloatingWindowTest(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>浮窗模块测试</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowFloatingWindowTest(false)}
            >
              <Ionicons name="close" size={24} color={colors.text.primary} />
            </TouchableOpacity>
          </View>
          <ExpoFloatingWindowTest />
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  welcomeSection: {
    padding: spacing.lg,
    backgroundColor: colors.primary + '10',
  },

  welcomeTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },

  welcomeSubtitle: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
  },

  section: {
    padding: spacing.lg,
  },

  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },

  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },

  seeAllText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
  },

  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },

  quickActionItem: {
    width: GRID_ITEM_WIDTH,
    padding: spacing.md,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: spacing.md,
  },

  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },

  quickActionTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    textAlign: 'center',
  },

  recentList: {
    backgroundColor: colors.background.primary,
  },

  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
  },

  recentItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  recentItemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },

  recentItemInfo: {
    flex: 1,
  },

  recentItemTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: spacing.xs / 2,
  },

  recentItemTime: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },

  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    paddingVertical: spacing.lg,
  },

  statItem: {
    alignItems: 'center',
  },

  statNumber: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
    marginBottom: spacing.xs,
  },

  statLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },

  modalContainer: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    paddingTop: spacing.xl,
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
  },

  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },

  closeButton: {
    padding: spacing.xs,
  },
});

export default HomeScreen;
