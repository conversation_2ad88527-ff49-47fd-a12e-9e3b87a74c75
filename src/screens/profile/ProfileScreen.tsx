import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { typography } from '@/styles';
import { useNavigation } from '@react-navigation/native';
import { RootStackParamList } from '@/types/navigation';
import { StackNavigationProp } from '@react-navigation/stack';

// 导入测试组件
import SimpleLocationTest from '@/components/testing/SimpleLocationTest';
import ExpoAudioTest from '@/components/testing/ExpoAudioTest';
import ExpoFloatingWindowTest from '@/components/testing/ExpoFloatingWindowTest';

type NavigationProp = StackNavigationProp<RootStackParamList>;

interface SettingItem {
  id: string;
  title: string;
  icon: number; // require() 返回的是 number 类型
  onPress?: () => void;
}

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();

  // 测试模态框状态
  const [showLocationTest, setShowLocationTest] = useState(false);
  const [showAudioTest, setShowAudioTest] = useState(false);
  const [showFloatingWindowTest, setShowFloatingWindowTest] = useState(false);

  // 常用功能列表，严格按照设计稿顺序
  const functionItems: SettingItem[] = [
    {
      id: 'recharge',
      title: '会员充值',
      icon: require('../../../assets/images/settings/icon-card.png'),
      onPress: () => {
        // TODO: 实现会员充值功能
      },
    },
    {
      id: 'customer-service',
      title: '联系客服',
      icon: require('../../../assets/images/settings/icon-headphone.png'),
      onPress: () => {
        // TODO: 实现联系客服功能
      },
    },
    {
      id: 'feedback',
      title: '用户意见反馈',
      icon: require('../../../assets/images/settings/icon-feedback.png'),
      onPress: () => {
        // TODO: 实现用户意见反馈功能
      },
    },
    {
      id: 'settings',
      title: '设置',
      icon: require('../../../assets/images/settings/icon-settings.png'),
      onPress: () => {
        navigation.navigate('Settings');
      },
    },
    {
      id: 'about',
      title: '关于',
      icon: require('../../../assets/images/settings/icon-info.png'),
      onPress: () => {
        // TODO: 实现关于功能
      },
    },
  ];

  // 开发测试功能列表
  const testItems: SettingItem[] = [
    {
      id: 'test-location',
      title: '定位模块测试',
      icon: require('../../../assets/images/settings/icon-info.png'), // 临时使用现有图标
      onPress: () => {
        setShowLocationTest(true);
      },
    },
    {
      id: 'test-audio',
      title: '录音模块测试',
      icon: require('../../../assets/images/settings/icon-headphone.png'), // 使用耳机图标
      onPress: () => {
        setShowAudioTest(true);
      },
    },
    {
      id: 'test-floating-window',
      title: '浮窗模块测试',
      icon: require('../../../assets/images/settings/icon-settings.png'), // 临时使用设置图标
      onPress: () => {
        setShowFloatingWindowTest(true);
      },
    },
  ];

  const renderFunctionItem = (item: SettingItem, index: number) => (
    <TouchableOpacity
      key={item.id}
      style={[
        styles.functionItem,
        index === functionItems.length - 1 && styles.lastFunctionItem,
      ]}
      onPress={item.onPress}
      activeOpacity={0.7}
    >
      <View style={styles.functionItemContent}>
        <View style={styles.functionIconContainer}>
          <Image source={item.icon} style={styles.functionIcon} />
        </View>
        <Text style={styles.functionTitle}>{item.title}</Text>
      </View>
      <View style={styles.functionArrow}>
        <Ionicons name="chevron-forward" size={20} color="#B0B2BF" />
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* 用户信息卡片 */}
        <View style={styles.userCard}>
          {/* 背景渐变 - 使用切图 */}
          <Image
            source={require('../../../assets/images/settings/card-background-large.png')}
            style={styles.cardBackground}
            resizeMode="stretch"
          />

          {/* 用户信息 */}
          <View style={styles.userInfoSection}>
            <TouchableOpacity
              onPress={() => navigation.navigate('ProfileEdit')}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['#C9E1FF', '#9FCAFF']}
                start={{ x: 0.981, y: 0.076 }}
                end={{ x: 0.028, y: 0.818 }}
                style={styles.avatarContainer}
              >
                <Image
                  source={require('../../../assets/images/settings/avatar-placeholder.png')}
                  style={styles.avatar}
                />
              </LinearGradient>
            </TouchableOpacity>
            <Text style={styles.phoneNumber}>189****3456</Text>
          </View>

          {/* VIP 信息和续费按钮 */}
          <View style={styles.vipSection}>
            <View style={styles.vipInfo}>
              <View style={styles.vipTitleRow}>
                <Image
                  source={require('../../../assets/images/settings/vip-text-gradient.png')}
                  style={styles.vipTitleImage}
                  resizeMode="contain"
                />
                <Text style={styles.vipExpiry}>2025.08.11 到期</Text>
              </View>
              <Text style={styles.vipSubtitle}>智享办公，高效进阶</Text>
            </View>
            <TouchableOpacity activeOpacity={0.8}>
              <LinearGradient
                colors={['#3053ED', '#5279FF', '#3EDDB6']}
                start={{ x: -0.12, y: -0.038 }}
                end={{ x: 1.024, y: 1.047 }}
                style={styles.renewButton}
              >
                <Text style={styles.renewButtonText}>立即续费</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>

          {/* VIP 标志 */}
          <Text style={styles.vipBadge}>VIP</Text>
        </View>

        {/* 常用功能区域 */}
        <View style={styles.functionsSection}>
          <Text style={styles.sectionTitle}>常用功能</Text>
          <View style={styles.functionsContainer}>
            {functionItems.map((item, index) =>
              renderFunctionItem(item, index)
            )}
          </View>
        </View>

        {/* 开发测试区域 */}
        <View style={styles.functionsSection}>
          <Text style={styles.sectionTitle}>开发测试</Text>
          <View style={styles.functionsContainer}>
            {testItems.map((item, index) => renderFunctionItem(item, index))}
          </View>
        </View>
      </ScrollView>

      {/* 定位测试模态框 */}
      <Modal
        visible={showLocationTest}
        animationType="slide"
        presentationStyle="formSheet"
        onRequestClose={() => setShowLocationTest(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>定位模块测试</Text>
            <TouchableOpacity
              onPress={() => setShowLocationTest(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color="#414352" />
            </TouchableOpacity>
          </View>
          <SimpleLocationTest />
        </View>
      </Modal>

      {/* 录音测试模态框 */}
      <Modal
        visible={showAudioTest}
        animationType="slide"
        presentationStyle="formSheet"
        onRequestClose={() => setShowAudioTest(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>录音模块测试</Text>
            <TouchableOpacity
              onPress={() => setShowAudioTest(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color="#414352" />
            </TouchableOpacity>
          </View>
          <ExpoAudioTest />
        </View>
      </Modal>

      {/* 浮窗测试模态框 */}
      <Modal
        visible={showFloatingWindowTest}
        animationType="slide"
        presentationStyle="formSheet"
        onRequestClose={() => setShowFloatingWindowTest(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>浮窗模块测试</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowFloatingWindowTest(false)}
            >
              <Ionicons name="close" size={24} color="#414352" />
            </TouchableOpacity>
          </View>
          <ExpoFloatingWindowTest />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F4F8FF',
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    paddingTop: 50, // 增加顶部间距，避免与状态栏重叠
    paddingHorizontal: 16,
    paddingBottom: 100, // 为底部导航留出空间
  },

  // 用户信息卡片样式
  userCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 16,
    padding: 16,
    position: 'relative',
    overflow: 'hidden', // 恢复隐藏，通过调整背景图片尺寸解决
    // iOS 阴影
    shadowColor: '#646778',
    shadowOffset: {
      width: 0,
      height: -1,
    },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    // Android 阴影
    elevation: 8,
  },

  cardBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '120%', // 扩展宽度确保右边不被裁切
    height: 88,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },

  userInfoSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 14,
    gap: 17,
  },

  avatarContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
  },

  avatar: {
    width: 60,
    height: 60,
    resizeMode: 'cover',
    marginTop: 1.5,
    marginLeft: -6,
  },

  phoneNumber: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600',
    color: '#2A2B33',
    lineHeight: 24,
  },

  vipSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },

  vipInfo: {
    flex: 1,
  },

  vipTitleRow: {
    flexDirection: 'row',
    alignItems: 'flex-start', // 顶部对齐而不是居中
    gap: 4,
    marginBottom: 4,
  },

  vipTitleImage: {
    width: 85, // 调整以匹配设计稿
    height: 20,
  },

  vipExpiry: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    fontWeight: '400',
    color: '#B0B2BF',
    lineHeight: 20,
    marginTop: 2, // 微调对齐
  },

  vipSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    fontWeight: '600',
    color: '#414352',
    lineHeight: 20,
  },

  renewButton: {
    borderRadius: 99,
    paddingHorizontal: 16,
    paddingVertical: 6,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
    height: 32,
  },

  renewButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    lineHeight: 20,
    textAlign: 'center',
  },

  vipBadge: {
    position: 'absolute',
    top: -1,
    right: 16,
    fontFamily: typography.fontFamily,
    fontSize: 56.57,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.7)',
    textShadowColor: '#FFFFFF',
    textShadowOffset: { width: -1.43, height: -1.43 },
    textShadowRadius: 0,
    height: 79,
    width: 89,
    textAlign: 'left',
    lineHeight: 79,
  },

  // 常用功能区域样式
  functionsSection: {
    flex: 1,
  },

  sectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    fontWeight: '400',
    color: '#9092A3',
    lineHeight: 20,
    marginBottom: 4,
    paddingHorizontal: 16,
  },

  functionsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
  },

  functionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 16,
    paddingRight: 16,
    paddingVertical: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#EBEBF0',
  },

  lastFunctionItem: {
    borderBottomWidth: 0,
  },

  functionItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  functionIconContainer: {
    width: 24,
    height: 24,
    marginRight: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },

  functionIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },

  functionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    fontWeight: '400',
    color: '#414352',
    lineHeight: 24,
    flex: 1,
  },

  functionArrow: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  // 模态框样式
  modalContainer: {
    flex: 1,
    backgroundColor: '#F4F8FF',
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingTop: 50, // 为状态栏留出空间
    borderBottomWidth: 0.5,
    borderBottomColor: '#EBEBF0',
    backgroundColor: '#FFFFFF',
  },

  modalTitle: {
    fontFamily: typography.fontFamily,
    fontSize: 18,
    fontWeight: '600',
    color: '#414352',
  },

  closeButton: {
    padding: 8,
  },
});

export default ProfileScreen;
