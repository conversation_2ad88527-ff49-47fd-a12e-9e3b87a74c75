{"name": "mobile-office-assistant", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "start:tunnel": "expo start --clear --tunnel", "start:lan": "expo start --lan", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "type-check": "tsc --noEmit", "prepare": "husky", "screenshot": "./scripts/debug-tools.sh screenshot", "record": "./scripts/debug-tools.sh record", "mirror": "./scripts/debug-tools.sh mirror", "backup": "./scripts/backup-native-code.sh backup", "restore": "./scripts/backup-native-code.sh restore", "check": "./scripts/backup-native-code.sh check", "eas-build-pre": "rm -rf android/app/src/main/java/com/gzai168/zipco/{audio,floating,location,audio_service} && mkdir -p android/app/src/main/java/com/gzai168/zipco/{audio,floating,location,audio_service} && mkdir -p android/app/src/main/res/drawable && cp -r native-code-backup/audio_module/* android/app/src/main/java/com/gzai168/zipco/audio/ && cp -r native-code-backup/floating_module/* android/app/src/main/java/com/gzai168/zipco/floating/ && cp -r native-code-backup/location_module/* android/app/src/main/java/com/gzai168/zipco/location/ && cp -r native-code-backup/audio_service/* android/app/src/main/java/com/gzai168/zipco/audio_service/ && cp -r native-code-backup/drawable/* android/app/src/main/res/drawable/ && cp native-code-backup/MainApplication.kt android/app/src/main/java/com/gzai168/zipco/MainApplication.kt && cp native-code-backup/AndroidManifest.xml android/app/src/main/AndroidManifest.xml"}, "dependencies": {"@anthropic-ai/claude-code": "^1.0.64", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "4.5.6", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.0.1", "@types/crypto-js": "^4.2.2", "axios": "^1.10.0", "crypto-js": "^4.2.0", "expo": "^53.0.0", "expo-av": "~15.1.7", "expo-blur": "~14.1.5", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.11", "expo-floating-window": "file:./modules/expo-floating-window", "expo-font": "^13.3.2", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-location": "~18.1.6", "expo-modules-core": "^2.5.0", "expo-realtime-audio": "file:./modules/expo-realtime-audio", "expo-sharing": "~13.1.5", "expo-status-bar": "~2.2.3", "jsencrypt": "^3.3.2", "prop-types": "^15.8.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-collapsible": "^1.6.2", "react-native-gesture-handler": "~2.24.0", "react-native-markdown-display": "^7.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-sse": "^1.2.1", "react-native-swipe-list-view": "^3.2.9", "react-native-toast-message": "^2.3.3", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "zustand": "^5.0.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/config-plugins": "^10.1.2", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.57.1", "eslint-import-resolver-babel-module": "^5.3.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "expo-module-scripts": "^4.1.9", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "typescript": "~5.8.3"}, "private": true, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}