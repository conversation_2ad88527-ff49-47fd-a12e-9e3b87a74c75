# Expo Modules API 迁移计划

**制定时间**: 2025年08月03日  
**项目**: office-assistant-hybrid  
**目标**: 将现有自定义原生模块迁移到Expo Modules API

---

## 📊 模块分析总结

| 模块 | 复杂度 | 迁移难度 | 预估工时 | 优先级 |
|------|--------|----------|----------|--------|
| **NativeLocationModule** | 简单 | 3/10 | 0.5-1天 | 🔥 高 (试点) |
| **RealtimeAudioModule** | 中等 | 6/10 | 2-3天 | 🎯 中 (核心) |
| **FloatingWindowModule** | 复杂 | 8/10 | 4-5天 | ⏳ 低 (最后) |

**总预估工时**: 7-9天

---

## 🚀 分阶段迁移策略

### Phase 1: 准备和试点 (1-2天)
**目标**: 建立迁移基础架构，验证可行性

#### 1.1 环境准备
- [ ] 安装 Expo Modules 开发工具
```bash
npx create-expo-module@latest --local
```
- [ ] 配置 TypeScript 支持
- [ ] 设置本地模块开发环境

#### 1.2 试点迁移 - Location Module
**选择原因**: 
- 复杂度最低，风险最小
- 可以验证整个迁移流程
- 为后续模块提供模板

**迁移步骤**:
1. 创建新的 Expo Module 结构
2. 迁移定位功能逻辑
3. 实现权限管理
4. 编写单元测试
5. 功能验证

**验收标准**:
- [ ] 所有原有功能正常工作
- [ ] 权限申请流程正确
- [ ] 类型安全完整
- [ ] 性能无回退

---

### Phase 2: 核心模块迁移 (3-4天)
**目标**: 迁移最重要的录音模块

#### 2.1 Audio Module 迁移
**技术重点**:
- 实时音频流处理
- 前台服务集成
- PCM数据优化传输
- 多线程安全

**迁移计划**:

**Day 1: 基础架构**
- [ ] 创建 ExpoRealtimeAudioModule
- [ ] 设计新的 TypeScript 接口
- [ ] 实现基础的录音开始/停止功能

**Day 2: 高级功能**
- [ ] 迁移 AudioStreamManager 逻辑
- [ ] 实现实时数据传输 (考虑 ArrayBuffer 替代 Base64)
- [ ] 前台服务集成

**Day 3: 优化和测试**
- [ ] 性能优化
- [ ] 内存泄漏检查
- [ ] 权限管理
- [ ] 全面功能测试

**验收标准**:
- [ ] 录音质量无损
- [ ] 实时传输延迟 < 50ms
- [ ] 后台录音稳定性
- [ ] 内存使用优化

---

### Phase 3: 复杂模块迁移 (4-5天)
**目标**: 迁移最复杂的浮窗模块

#### 3.1 Floating Window Module 迁移
**技术重点**:
- 系统级浮窗权限
- 复杂的生命周期管理
- 多组件协调机制
- UI 渲染性能

**迁移计划**:

**Day 1-2: 核心架构重构**
- [ ] 设计新的模块架构
- [ ] 重构 FloatingWindowManager
- [ ] 实现权限管理系统

**Day 3-4: 高级功能实现**
- [ ] CapsuleCoordinator 迁移
- [ ] 位置同步机制
- [ ] 生命周期管理
- [ ] 事件系统重构

**Day 5: 测试和优化**
- [ ] 内存泄漏测试
- [ ] 性能优化
- [ ] 兼容性测试
- [ ] 边界情况处理

**验收标准**:
- [ ] 浮窗显示稳定
- [ ] 位置同步精确
- [ ] 无内存泄漏
- [ ] 权限处理完善

---

### Phase 4: 集成和清理 (1天)
**目标**: 完成最终集成和清理工作

#### 4.1 系统集成
- [ ] 更新 MainApplication.kt
- [ ] 配置新的模块注册
- [ ] 更新 JavaScript 调用代码
- [ ] Config Plugin 实现

#### 4.2 清理工作
- [ ] 移除旧的 ReactPackage 实现
- [ ] 清理 native-code-backup 目录
- [ ] 更新文档
- [ ] 代码审查

---

## 🛠️ 技术实施细节

### Expo Module 项目结构
```
modules/
├── expo-realtime-audio/
│   ├── android/
│   │   └── src/main/java/
│   ├── ios/          # 预留iOS支持
│   ├── src/
│   │   └── index.ts
│   └── expo-module.config.json
├── expo-floating-window/
└── expo-location/
```

### Config Plugin 设计
```typescript
// app.json
{
  "expo": {
    "plugins": [
      ["./modules/expo-realtime-audio/plugin", {
        "permissions": ["RECORD_AUDIO", "FOREGROUND_SERVICE"]
      }],
      ["./modules/expo-floating-window/plugin", {
        "permissions": ["SYSTEM_ALERT_WINDOW"]
      }]
    ]
  }
}
```

### TypeScript 接口设计
```typescript
// 录音模块
export interface AudioConfig {
  sampleRate: number;
  interval: number;
  encoding?: 'PCM_16BIT';
}

export interface AudioModule {
  startRecording(config: AudioConfig): Promise<{ success: boolean; message: string }>;
  stopRecording(): Promise<{ success: boolean; message: string }>;
  isRecording(): Promise<boolean>;
}

// 事件类型
export interface AudioEvents {
  onAudioData: (data: ArrayBuffer) => void;
  onError: (error: Error) => void;
}
```

---

## 🎯 验收标准

### 功能验收
- [ ] **录音模块**: 实时录音、数据传输、前台服务
- [ ] **浮窗模块**: 权限管理、位置同步、生命周期
- [ ] **定位模块**: 精确定位、权限处理、错误管理

### 性能验收
- [ ] **构建时间**: 无显著增加
- [ ] **运行时性能**: 不低于现有实现
- [ ] **内存使用**: 优化或持平
- [ ] **包大小**: 控制增长

### 代码质量验收
- [ ] **类型安全**: 100% TypeScript 覆盖
- [ ] **单元测试**: 核心功能测试覆盖
- [ ] **文档完整**: API 文档和使用示例
- [ ] **代码审查**: 通过团队代码审查

---

## ⚠️ 风险控制

### 主要风险
1. **兼容性风险**: 新旧模块接口差异
2. **性能风险**: 数据传输方式变更
3. **权限风险**: 不同 Android 版本权限处理
4. **稳定性风险**: 复杂组件重构

### 风险缓解
1. **并行开发**: 保留旧模块作为备份
2. **渐进迁移**: 逐模块验证，降低整体风险
3. **充分测试**: 多设备、多场景测试
4. **回退计划**: 完整的回退方案

---

## 📅 时间安排

| 阶段 | 时间安排 | 里程碑 |
|------|----------|--------|
| Phase 1 | Day 1-2 | Location Module 迁移完成 |
| Phase 2 | Day 3-5 | Audio Module 迁移完成 |
| Phase 3 | Day 6-10 | Floating Module 迁移完成 |
| Phase 4 | Day 11 | 整体集成和清理完成 |

**预计完成时间**: 2周 (11个工作日)

---

## 🎉 预期收益

### 短期收益
- [ ] **开发效率**: 减少手动 native-code 管理
- [ ] **类型安全**: 更好的 TypeScript 支持
- [ ] **错误处理**: 更清晰的错误信息和调试

### 长期收益
- [ ] **维护性**: 标准化的模块实现
- [ ] **扩展性**: 更容易添加新功能
- [ ] **性能**: 新架构下的性能优化
- [ ] **iOS 支持**: 为未来 iOS 开发铺路

---

**准备开始迁移工作！** 🚀