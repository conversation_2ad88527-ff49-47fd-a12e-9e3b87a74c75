const { withAndroidManifest, AndroidConfig } = require('@expo/config-plugins');

const { addPermission } = AndroidConfig.Permissions;

/**
 * Expo Config Plugin for expo-floating-window
 * 自动配置浮窗权限和相关配置
 */
const withExpoFloatingWindow = (config) => {
  // 配置Android权限
  config = withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;

    // 添加系统叠加窗口权限
    addPermission(androidManifest, 'android.permission.SYSTEM_ALERT_WINDOW');

    // 添加应用程序设置权限 (用于引导用户开启浮窗权限)
    addPermission(
      androidManifest,
      'android.permission.REQUEST_INSTALL_PACKAGES'
    );

    // 在application节点中添加相关配置
    const application = androidManifest.manifest.application?.[0];
    if (application) {
      // 确保application的$属性存在
      if (!application.$) {
        application.$ = {};
      }

      // 添加对浮窗的支持配置
      // 允许应用在锁屏上显示
      if (!application.$['android:showOnLockScreen']) {
        application.$['android:showOnLockScreen'] = 'true';
      }

      // 确保activity数组存在并为主Activity添加浮窗相关配置
      if (application.activity) {
        const mainActivity = application.activity.find(
          (activity) => activity.$?.['android:name'] === '.MainActivity'
        );

        if (mainActivity && mainActivity.$) {
          // 允许Activity显示在其他应用之上
          mainActivity.$['android:showWhenLocked'] = 'true';
          mainActivity.$['android:turnScreenOn'] = 'true';
        }
      }
    }

    return config;
  });

  return config;
};

module.exports = withExpoFloatingWindow;
module.exports.withExpoFloatingWindow = withExpoFloatingWindow;
