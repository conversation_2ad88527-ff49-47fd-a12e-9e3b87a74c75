import { DeviceEventEmitter } from 'react-native';
import ExpoFloatingWindowModule from './ExpoFloatingWindowModule';

// 导出接口类型
export interface Subscription {
  remove(): void;
}

export interface FloatingWindowConfig {
  width?: number; // 窗口宽度（像素）
  height?: number; // 窗口高度（像素）
  x?: number; // 初始X坐标
  y?: number; // 初始Y坐标
  draggable?: boolean; // 是否可拖动，默认true
  resizable?: boolean; // 是否可调整大小，默认false
  content?: string; // 窗口内容（HTML或URL）
  title?: string; // 窗口标题
}

export interface FloatingWindowInfo {
  id: string; // 窗口唯一标识
  width: number; // 当前宽度
  height: number; // 当前高度
  x: number; // 当前X坐标
  y: number; // 当前Y坐标
  visible: boolean; // 是否可见
  title?: string; // 窗口标题
}

export interface FloatingWindowResult {
  success: boolean;
  message?: string;
  windowId?: string;
}

export interface PermissionStatus {
  granted: boolean;
  canRequest: boolean;
  status: 'granted' | 'denied' | 'unknown';
}

export interface WindowMoveEvent {
  windowId: string;
  x: number;
  y: number;
}

export interface WindowResizeEvent {
  windowId: string;
  width: number;
  height: number;
}

export interface WindowClickEvent {
  windowId: string;
  type: 'click' | 'double_click';
}

class ExpoFloatingWindow {
  private moveSubscription: Subscription | null = null;
  private resizeSubscription: Subscription | null = null;
  private clickSubscription: Subscription | null = null;

  /**
   * 检查系统悬浮窗权限
   */
  async checkPermissions(): Promise<PermissionStatus> {
    try {
      return await ExpoFloatingWindowModule.checkPermissions();
    } catch (error) {
      console.error('检查浮窗权限失败:', error);
      return {
        granted: false,
        canRequest: true,
        status: 'unknown',
      };
    }
  }

  /**
   * 请求系统悬浮窗权限
   */
  async requestPermissions(): Promise<PermissionStatus> {
    try {
      return await ExpoFloatingWindowModule.requestPermissions();
    } catch (error) {
      console.error('请求浮窗权限失败:', error);
      return {
        granted: false,
        canRequest: false,
        status: 'denied',
      };
    }
  }

  /**
   * 创建浮窗
   */
  async createWindow(
    config: FloatingWindowConfig = {}
  ): Promise<FloatingWindowResult> {
    const finalConfig: FloatingWindowConfig = {
      width: 300,
      height: 200,
      x: 100,
      y: 100,
      draggable: true,
      resizable: false,
      content: '<div>默认浮窗内容</div>',
      title: '浮窗',
      ...config,
    };

    try {
      const result = await ExpoFloatingWindowModule.createWindow(finalConfig);
      return result;
    } catch (error) {
      console.error('创建浮窗失败:', error);
      return {
        success: false,
        message: `创建浮窗失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 显示浮窗
   */
  async showWindow(windowId: string): Promise<FloatingWindowResult> {
    try {
      return await ExpoFloatingWindowModule.showWindow(windowId);
    } catch (error) {
      console.error('显示浮窗失败:', error);
      return {
        success: false,
        message: `显示浮窗失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 隐藏浮窗
   */
  async hideWindow(windowId: string): Promise<FloatingWindowResult> {
    try {
      return await ExpoFloatingWindowModule.hideWindow(windowId);
    } catch (error) {
      console.error('隐藏浮窗失败:', error);
      return {
        success: false,
        message: `隐藏浮窗失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 关闭并销毁浮窗
   */
  async closeWindow(windowId: string): Promise<FloatingWindowResult> {
    try {
      return await ExpoFloatingWindowModule.closeWindow(windowId);
    } catch (error) {
      console.error('关闭浮窗失败:', error);
      return {
        success: false,
        message: `关闭浮窗失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 更新浮窗位置
   */
  async moveWindow(
    windowId: string,
    x: number,
    y: number
  ): Promise<FloatingWindowResult> {
    try {
      return await ExpoFloatingWindowModule.moveWindow(windowId, x, y);
    } catch (error) {
      console.error('移动浮窗失败:', error);
      return {
        success: false,
        message: `移动浮窗失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 更新浮窗大小
   */
  async resizeWindow(
    windowId: string,
    width: number,
    height: number
  ): Promise<FloatingWindowResult> {
    try {
      return await ExpoFloatingWindowModule.resizeWindow(
        windowId,
        width,
        height
      );
    } catch (error) {
      console.error('调整浮窗大小失败:', error);
      return {
        success: false,
        message: `调整浮窗大小失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 更新浮窗内容
   */
  async updateContent(
    windowId: string,
    content: string
  ): Promise<FloatingWindowResult> {
    try {
      return await ExpoFloatingWindowModule.updateContent(windowId, content);
    } catch (error) {
      console.error('更新浮窗内容失败:', error);
      return {
        success: false,
        message: `更新浮窗内容失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 获取浮窗信息
   */
  async getWindowInfo(windowId: string): Promise<FloatingWindowInfo | null> {
    try {
      return await ExpoFloatingWindowModule.getWindowInfo(windowId);
    } catch (error) {
      console.error('获取浮窗信息失败:', error);
      return null;
    }
  }

  /**
   * 获取所有浮窗列表
   */
  async getAllWindows(): Promise<FloatingWindowInfo[]> {
    try {
      return await ExpoFloatingWindowModule.getAllWindows();
    } catch (error) {
      console.error('获取浮窗列表失败:', error);
      return [];
    }
  }

  /**
   * 添加窗口移动事件监听器
   */
  addMoveListener(listener: (event: WindowMoveEvent) => void): Subscription {
    if (this.moveSubscription) {
      this.moveSubscription.remove();
    }

    this.moveSubscription = DeviceEventEmitter.addListener(
      'onWindowMove',
      (eventData: WindowMoveEvent) => {
        listener(eventData);
      }
    );

    return this.moveSubscription;
  }

  /**
   * 添加窗口大小变化事件监听器
   */
  addResizeListener(
    listener: (event: WindowResizeEvent) => void
  ): Subscription {
    if (this.resizeSubscription) {
      this.resizeSubscription.remove();
    }

    this.resizeSubscription = DeviceEventEmitter.addListener(
      'onWindowResize',
      (eventData: WindowResizeEvent) => {
        listener(eventData);
      }
    );

    return this.resizeSubscription;
  }

  /**
   * 添加窗口点击事件监听器
   */
  addClickListener(listener: (event: WindowClickEvent) => void): Subscription {
    if (this.clickSubscription) {
      this.clickSubscription.remove();
    }

    this.clickSubscription = DeviceEventEmitter.addListener(
      'onWindowClick',
      (eventData: WindowClickEvent) => {
        listener(eventData);
      }
    );

    return this.clickSubscription;
  }

  /**
   * 移除所有事件监听器
   */
  removeAllListeners(): void {
    if (this.moveSubscription) {
      this.moveSubscription.remove();
      this.moveSubscription = null;
    }

    if (this.resizeSubscription) {
      this.resizeSubscription.remove();
      this.resizeSubscription = null;
    }

    if (this.clickSubscription) {
      this.clickSubscription.remove();
      this.clickSubscription = null;
    }
  }
}

// 导出单例实例
export default new ExpoFloatingWindow();

// 同时导出类，支持多实例使用
export { ExpoFloatingWindow };
