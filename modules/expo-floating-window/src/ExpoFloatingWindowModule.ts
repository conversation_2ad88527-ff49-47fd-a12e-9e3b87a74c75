import { requireNativeModule } from 'expo-modules-core';

export interface FloatingWindowConfig {
  width?: number; // 窗口宽度（像素）
  height?: number; // 窗口高度（像素）
  x?: number; // 初始X坐标
  y?: number; // 初始Y坐标
  draggable?: boolean; // 是否可拖动，默认true
  resizable?: boolean; // 是否可调整大小，默认false
  content?: string; // 窗口内容（HTML或URL）
  title?: string; // 窗口标题
}

export interface FloatingWindowInfo {
  id: string; // 窗口唯一标识
  width: number; // 当前宽度
  height: number; // 当前高度
  x: number; // 当前X坐标
  y: number; // 当前Y坐标
  visible: boolean; // 是否可见
  title?: string; // 窗口标题
}

export interface FloatingWindowResult {
  success: boolean;
  message?: string;
  windowId?: string;
}

export interface PermissionStatus {
  granted: boolean;
  canRequest: boolean;
  status: 'granted' | 'denied' | 'unknown';
}

export interface ExpoFloatingWindowModuleInterface {
  /**
   * 检查系统悬浮窗权限
   */
  checkPermissions(): Promise<PermissionStatus>;

  /**
   * 请求系统悬浮窗权限
   */
  requestPermissions(): Promise<PermissionStatus>;

  /**
   * 创建浮窗
   */
  createWindow(config: FloatingWindowConfig): Promise<FloatingWindowResult>;

  /**
   * 显示浮窗
   */
  showWindow(windowId: string): Promise<FloatingWindowResult>;

  /**
   * 隐藏浮窗
   */
  hideWindow(windowId: string): Promise<FloatingWindowResult>;

  /**
   * 关闭并销毁浮窗
   */
  closeWindow(windowId: string): Promise<FloatingWindowResult>;

  /**
   * 更新浮窗位置
   */
  moveWindow(
    windowId: string,
    x: number,
    y: number
  ): Promise<FloatingWindowResult>;

  /**
   * 更新浮窗大小
   */
  resizeWindow(
    windowId: string,
    width: number,
    height: number
  ): Promise<FloatingWindowResult>;

  /**
   * 更新浮窗内容
   */
  updateContent(
    windowId: string,
    content: string
  ): Promise<FloatingWindowResult>;

  /**
   * 获取浮窗信息
   */
  getWindowInfo(windowId: string): Promise<FloatingWindowInfo | null>;

  /**
   * 获取所有浮窗列表
   */
  getAllWindows(): Promise<FloatingWindowInfo[]>;
}

// Export the native module object
export default requireNativeModule<ExpoFloatingWindowModuleInterface>(
  'ExpoFloatingWindow'
);
