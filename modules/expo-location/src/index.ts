import ExpoLocationModule from './ExpoLocationModule';

export interface LocationConfig {
  accuracy?: 'high' | 'medium' | 'low';
  timeout?: number;
  maximumAge?: number;
}

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  altitude?: number;
  accuracy?: number;
  altitudeAccuracy?: number;
  heading?: number;
  speed?: number;
}

export interface LocationResult {
  success: boolean;
  coordinates?: LocationCoordinates;
  message?: string;
  timestamp?: number;
}

export interface LocationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: 'granted' | 'denied' | 'never_ask_again';
}

export default {
  /**
   * 请求位置权限
   */
  async requestPermissions(): Promise<LocationPermissionStatus> {
    return await ExpoLocationModule.requestPermissions();
  },

  /**
   * 检查权限状态
   */
  async getPermissions(): Promise<LocationPermissionStatus> {
    return await ExpoLocationModule.getPermissions();
  },

  /**
   * 获取当前位置
   */
  async getCurrentPosition(config?: LocationConfig): Promise<LocationResult> {
    const finalConfig: LocationConfig = {
      accuracy: 'high' as const,
      timeout: 15000,
      maximumAge: 60000,
      ...config
    };
    return await ExpoLocationModule.getCurrentPosition(finalConfig);
  },

  /**
   * 开始监听位置变化
   */
  async startLocationUpdates(config?: LocationConfig): Promise<{ success: boolean; message?: string }> {
    const finalConfig: LocationConfig = {
      accuracy: 'high' as const,
      timeout: 15000,
      maximumAge: 5000,
      ...config
    };
    return await ExpoLocationModule.startLocationUpdates(finalConfig);
  },

  /**
   * 停止监听位置变化
   */
  async stopLocationUpdates(): Promise<{ success: boolean; message?: string }> {
    return await ExpoLocationModule.stopLocationUpdates();
  },

  /**
   * 检查位置服务是否启用
   */
  async isLocationEnabled(): Promise<boolean> {
    return await ExpoLocationModule.isLocationEnabled();
  }
};