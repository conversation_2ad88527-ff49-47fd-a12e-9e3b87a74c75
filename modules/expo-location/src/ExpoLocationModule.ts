import { requireNativeModule } from 'expo-modules-core';

export interface LocationConfig {
  accuracy?: 'high' | 'medium' | 'low';
  timeout?: number;
  maximumAge?: number;
}

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  altitude?: number;
  accuracy?: number;
  altitudeAccuracy?: number;
  heading?: number;
  speed?: number;
}

export interface LocationResult {
  success: boolean;
  coordinates?: LocationCoordinates;
  message?: string;
  timestamp?: number;
}

export interface LocationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: 'granted' | 'denied' | 'never_ask_again';
}

export interface ExpoLocationModuleInterface {
  requestPermissions(): Promise<LocationPermissionStatus>;
  getPermissions(): Promise<LocationPermissionStatus>;
  getCurrentPosition(config: LocationConfig): Promise<LocationResult>;
  startLocationUpdates(config: LocationConfig): Promise<{ success: boolean; message?: string }>;
  stopLocationUpdates(): Promise<{ success: boolean; message?: string }>;
  isLocationEnabled(): Promise<boolean>;
}

// Export the native module object
export default requireNativeModule<ExpoLocationModuleInterface>('ExpoLocation');