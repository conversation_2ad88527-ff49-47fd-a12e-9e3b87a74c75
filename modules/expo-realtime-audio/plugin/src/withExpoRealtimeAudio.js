const { withAndroidManifest, AndroidConfig } = require('@expo/config-plugins');

const { addPermission } = AndroidConfig.Permissions;

/**
 * Expo Config Plugin for expo-realtime-audio
 * 自动配置录音权限和前台服务
 */
const withExpoRealtimeAudio = (config) => {
  // 配置Android权限和服务
  config = withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;

    // 添加录音权限
    addPermission(androidManifest, 'android.permission.RECORD_AUDIO');

    // 添加前台服务权限 (Android 9.0+)
    addPermission(androidManifest, 'android.permission.FOREGROUND_SERVICE');

    // 添加音频前台服务类型权限 (Android 10.0+)
    addPermission(
      androidManifest,
      'android.permission.FOREGROUND_SERVICE_MICROPHONE'
    );

    // 在application节点中添加前台服务声明
    const application = androidManifest.manifest.application?.[0];
    if (application) {
      // 确保services数组存在
      if (!application.service) {
        application.service = [];
      }

      // 检查是否已存在AudioRecordingService
      const existingService = application.service.find(
        (service) =>
          service.$?.['android:name'] ===
          'com.gzai168.zipco.audio.AudioRecordingService'
      );

      if (!existingService) {
        // 添加AudioRecordingService服务声明
        application.service.push({
          $: {
            'android:name': 'com.gzai168.zipco.audio.AudioRecordingService',
            'android:enabled': 'true',
            'android:exported': 'false',
            'android:foregroundServiceType': 'microphone',
          },
        });
      }
    }

    return config;
  });

  return config;
};

module.exports = withExpoRealtimeAudio;
module.exports.withExpoRealtimeAudio = withExpoRealtimeAudio;
