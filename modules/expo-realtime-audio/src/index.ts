import { DeviceEventEmitter } from 'react-native';
import ExpoRealtimeAudioModule from './ExpoRealtimeAudioModule';

// 定义事件监听器类型
export interface Subscription {
  remove(): void;
}

export interface AudioConfig {
  sampleRate?: number;    // 采样率，默认16000Hz
  interval?: number;      // 数据发送间隔，默认40ms
  encoding?: 'PCM_16BIT'; // 编码格式
  enableForegroundService?: boolean; // 是否启用前台服务，默认true
}

export interface AudioResult {
  success: boolean;
  message?: string;
}

export interface AudioStreamData {
  data: string;          // Base64编码的PCM数据
  timestamp: number;     // 时间戳
  size: number;          // 数据大小（字节）
}

export interface AudioErrorEvent {
  message: string;
  code?: string;
}

// 使用React Native的DeviceEventEmitter

class ExpoRealtimeAudio {
  private audioDataSubscription: Subscription | null = null;
  private errorSubscription: Subscription | null = null;

  /**
   * 开始录音
   */
  async startRecording(config: AudioConfig = {}): Promise<AudioResult> {
    const finalConfig: AudioConfig = {
      sampleRate: 16000,    // 讯飞推荐采样率
      interval: 40,         // 讯飞要求40ms间隔
      encoding: 'PCM_16BIT', // 16位PCM编码
      enableForegroundService: true, // 默认启用前台服务
      ...config
    };

    try {
      const result = await ExpoRealtimeAudioModule.startRecording(finalConfig);
      return result;
    } catch (error) {
      console.error('开始录音失败:', error);
      return {
        success: false,
        message: `开始录音失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 停止录音
   */
  async stopRecording(): Promise<AudioResult> {
    try {
      const result = await ExpoRealtimeAudioModule.stopRecording();
      // 停止录音时清除监听器
      this.removeAllListeners();
      return result;
    } catch (error) {
      console.error('停止录音失败:', error);
      return {
        success: false,
        message: `停止录音失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 检查录音状态
   */
  async isRecording(): Promise<boolean> {
    try {
      return await ExpoRealtimeAudioModule.isRecording();
    } catch (error) {
      console.error('检查录音状态失败:', error);
      return false;
    }
  }

  /**
   * 获取录音时长（秒）
   */
  async getRecordingDuration(): Promise<number> {
    try {
      return await ExpoRealtimeAudioModule.getRecordingDuration();
    } catch (error) {
      console.error('获取录音时长失败:', error);
      return 0;
    }
  }

  /**
   * 添加音频数据监听器
   */
  addAudioDataListener(listener: (data: AudioStreamData) => void): Subscription {
    if (this.audioDataSubscription) {
      this.audioDataSubscription.remove();
    }
    
    this.audioDataSubscription = DeviceEventEmitter.addListener('onAudioData', (eventData: {data: string}) => {
      // Kotlin发送的是 mapOf("data" to base64Data)，所以接收的是对象
      const base64Data = eventData.data;
      const audioData: AudioStreamData = {
        data: base64Data,
        timestamp: Date.now(),
        size: this.calculateDataSize(base64Data)
      };
      listener(audioData);
    });

    return this.audioDataSubscription;
  }

  /**
   * 添加错误监听器
   */
  addErrorListener(listener: (error: AudioErrorEvent) => void): Subscription {
    if (this.errorSubscription) {
      this.errorSubscription.remove();
    }

    this.errorSubscription = DeviceEventEmitter.addListener('onError', (eventData: {message: string}) => {
      // Kotlin发送的是 mapOf("message" to errorMessage)
      const error: AudioErrorEvent = {
        message: eventData.message,
        code: 'AUDIO_ERROR'
      };
      listener(error);
    });

    return this.errorSubscription;
  }

  /**
   * 移除所有监听器
   */
  removeAllListeners(): void {
    if (this.audioDataSubscription) {
      this.audioDataSubscription.remove();
      this.audioDataSubscription = null;
    }
    
    if (this.errorSubscription) {
      this.errorSubscription.remove();
      this.errorSubscription = null;
    }
  }

  /**
   * 计算Base64数据的实际字节大小
   */
  private calculateDataSize(base64Data: string): number {
    // Base64编码：每4个字符代表3个字节
    // 去除padding字符计算实际大小
    const cleanData = base64Data.replace(/=/g, '');
    return Math.floor((cleanData.length * 3) / 4);
  }

  /**
   * 转换Base64为ArrayBuffer (用于高级处理)
   */
  base64ToArrayBuffer(base64: string): ArrayBuffer {
    try {
      const binaryString = atob(base64);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      return bytes.buffer;
    } catch (error) {
      console.error('Base64转换失败:', error);
      return new ArrayBuffer(0);
    }
  }

  /**
   * 获取音频数据统计信息
   */
  getAudioStats(base64Data: string): {
    sizeBytes: number;
    sizeKB: number;
    estimatedDurationMs: number;
  } {
    const sizeBytes = this.calculateDataSize(base64Data);
    const sizeKB = sizeBytes / 1024;
    
    // 基于16kHz采样率，16bit，单声道计算时长
    // 每秒数据量 = 16000 * 2 bytes = 32000 bytes/s
    const estimatedDurationMs = (sizeBytes / 32000) * 1000;

    return {
      sizeBytes,
      sizeKB: Math.round(sizeKB * 100) / 100,
      estimatedDurationMs: Math.round(estimatedDurationMs)
    };
  }
}

// 导出单例实例
export default new ExpoRealtimeAudio();

// 同时导出类，支持多实例使用
export { ExpoRealtimeAudio };