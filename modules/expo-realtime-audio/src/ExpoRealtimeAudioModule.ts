import { requireNativeModule } from 'expo-modules-core';

export interface AudioConfig {
  sampleRate?: number;    // 采样率，默认16000Hz (讯飞推荐)
  interval?: number;      // 数据发送间隔，默认40ms (讯飞要求)
  encoding?: 'PCM_16BIT'; // 编码格式，目前仅支持16位PCM
  enableForegroundService?: boolean; // 是否启用前台服务，默认true
}

export interface AudioResult {
  success: boolean;
  message?: string;
}

export interface AudioStreamData {
  data: string;          // Base64编码的PCM数据
  timestamp: number;     // 时间戳
  size: number;          // 数据大小（字节）
}

export interface ExpoRealtimeAudioModuleInterface {
  /**
   * 开始录音
   * @param config 录音配置
   * @returns Promise<AudioResult>
   */
  startRecording(config: AudioConfig): Promise<AudioResult>;

  /**
   * 停止录音
   * @returns Promise<AudioResult>
   */
  stopRecording(): Promise<AudioResult>;

  /**
   * 检查录音状态
   * @returns Promise<boolean>
   */
  isRecording(): Promise<boolean>;

  /**
   * 获取录音时长（秒）
   * @returns Promise<number>
   */
  getRecordingDuration(): Promise<number>;

  /**
   * 添加音频数据监听器
   * @param listener 音频数据回调函数
   */
  addAudioDataListener(listener: (data: AudioStreamData) => void): void;

  /**
   * 添加错误监听器
   * @param listener 错误回调函数
   */
  addErrorListener(listener: (error: { message: string; code?: string }) => void): void;

  /**
   * 移除所有监听器
   */
  removeAllListeners(): void;
}

// Export the native module object
export default requireNativeModule<ExpoRealtimeAudioModuleInterface>('ExpoRealtimeAudio');