{"name": "expo-realtime-audio", "version": "1.0.0", "description": "Local Expo Realtime Audio Module for high-quality audio recording", "main": "./src/index.ts", "types": "./src/index.ts", "plugin": "./plugin/src/index.js", "scripts": {"build:plugin": "tsc --build plugin"}, "dependencies": {"expo-modules-core": "^2.5.0"}, "peerDependencies": {"expo": "*"}, "devDependencies": {"@expo/config-plugins": "^8.0.0", "typescript": "~5.8.3"}}